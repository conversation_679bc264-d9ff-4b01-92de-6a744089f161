<!DOCTYPE html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>DevHub - Developer & Designer Community</title>
    
    <meta name="description" content="Join the ultimate community hub for developers and designers to share work, collaborate, and showcase projects. Connect, create, and inspire." />
    <meta name="author" content="DevHub Community" />

    <meta property="og:title" content="DevHub - Developer & Designer Community" />
    <meta property="og:description" content="Join the ultimate community hub for developers and designers to share work, collaborate, and showcase projects. Connect, create, and inspire." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://devhub.community" />
    <meta property="og:image" content="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=1200&h=630&fit=crop" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />	
    <meta property="og:image:alt" content="DevHub - Developer & Designer Community Platform" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="DevHub - Developer & Designer Community" />
    <meta name="twitter:description" content="Join the ultimate community hub for developers and designers to share work, collaborate, and showcase projects. Connect, create, and inspire." />
    <meta name="twitter:site" content="@devhub" />
    <meta name="twitter:image" content="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=1200&h=630&fit=crop" />

    <link rel="canonical" href="https://devhub.community" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

     <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "DevHub - Developer & Designer Community",
      "url": "https://devhub.community",
      "description": "Join the ultimate community hub for developers and designers to share work, collaborate, and showcase projects. Connect, create, and inspire.",
      "publisher": {
        "@type": "Organization",
        "name": "DevHub Community"
      }
    }
    </script>

  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
