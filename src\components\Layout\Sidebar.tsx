import React from 'react';
import { motion } from 'framer-motion';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  Home,
  Search,
  Bell,
  MessageCircle,
  User,
  Settings,
  Users,
  Compass,
  Plus,
  Code,
  LogOut
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

const Sidebar: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();

  const navItems = [
    { icon: Home, label: 'Dashboard', path: '/dashboard' },
    { icon: Code, label: 'Landing Page', path: '/' },
    { icon: Search, label: 'Search', path: '/search' },
    { icon: Compass, label: 'Explore', path: '/explore' },
    { icon: Users, label: 'Community', path: '/community' },
    { icon: Bell, label: 'Notifications', path: '/notifications', badge: 3 },
    { icon: MessageCircle, label: 'Messages', path: '/messages', badge: 2 },
    { icon: User, label: 'Profile', path: '/profile' },
    { icon: Settings, label: 'Settings', path: '/settings' },
  ];

  return (
    <motion.aside
      initial={{ x: -300 }}
      animate={{ x: 0 }}
      className="fixed left-0 top-0 z-40 w-64 h-screen bg-dark-900 border-r border-dark-700 overflow-y-auto"
    >
      <div className="p-6">
        {/* Logo */}
        <Link to="/dashboard" className="flex items-center space-x-2 mb-8">
          <Code className="w-8 h-8 text-neon-blue" />
          <span className="text-2xl font-bold bg-gradient-to-r from-neon-blue to-neon-purple bg-clip-text text-transparent">
            DevHub
          </span>
        </Link>

        {/* Create Button */}
        <Link to="/create-project">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="w-full flex items-center space-x-3 px-4 py-3 bg-gradient-to-r from-neon-blue to-neon-purple rounded-xl font-semibold mb-6"
          >
            <Plus className="w-5 h-5" />
            <span>Create Project</span>
          </motion.button>
        </Link>

        {/* Navigation */}
        <nav className="space-y-2">
          {navItems.map((item) => {
            const isActive = location.pathname === item.path;
            return (
              <Link key={item.path} to={item.path}>
                <motion.div
                  whileHover={{ x: 5 }}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-xl transition-colors ${
                    isActive
                      ? 'bg-neon-blue/20 text-neon-blue'
                      : 'text-gray-400 hover:text-white hover:bg-dark-800'
                  }`}
                >
                  <item.icon className="w-5 h-5" />
                  <span>{item.label}</span>
                  {item.badge && (
                    <span className="ml-auto bg-neon-pink text-white text-xs rounded-full px-2 py-1">
                      {item.badge}
                    </span>
                  )}
                </motion.div>
              </Link>
            );
          })}
        </nav>

        {/* User Info */}
        {user && (
          <div className="mt-8 p-4 bg-dark-800 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <img
                src={user.avatar}
                alt={user.name}
                className="w-10 h-10 rounded-full"
              />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">{user.name}</p>
                <p className="text-xs text-gray-400 truncate">{user.email}</p>
              </div>
            </div>

            {/* Sign Out Button */}
            <motion.button
              onClick={() => {
                logout();
                // Force a complete page reload to the landing page
                window.location.href = '/';
              }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="w-full flex items-center space-x-2 px-3 py-2 text-gray-400 hover:text-white hover:bg-dark-700 rounded-lg transition-colors"
            >
              <LogOut className="w-4 h-4" />
              <span className="text-sm">Sign Out</span>
            </motion.button>
          </div>
        )}
      </div>
    </motion.aside>
  );
};

export default Sidebar;
