import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Sidebar from '../components/Layout/Sidebar';
import TopBar from '../components/Layout/TopBar';
import { useAuth } from '../contexts/AuthContext';
import { Heart, MessageCircle, Share, Bookmark, MoreHorizontal, Code, Palette, Star, Eye } from 'lucide-react';

const Dashboard: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user } = useAuth();

  return (
    <div className="flex h-screen bg-dark-950">
      <Sidebar />
      
      <div className="flex-1 flex flex-col ml-64">
        <TopBar onToggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        
        <main className="flex-1 overflow-y-auto pt-16">
          <div className="max-w-4xl mx-auto px-6 py-8">
            {/* Welcome Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-8"
            >
              <h1 className="text-3xl font-bold text-white mb-2">
                Welcome back{user?.name ? `, ${user.name}` : ''}! 👋
              </h1>
              <p className="text-gray-400">Discover amazing projects and connect with the community</p>
            </motion.div>

            {/* Quick Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8"
            >
              {[
                { label: 'Following', value: user?.following?.toString() || '0', color: 'neon-blue' },
                { label: 'Followers', value: user?.followers?.toString() || '0', color: 'neon-purple' },
                { label: 'Projects', value: user?.projects?.toString() || '0', color: 'neon-teal' },
                { label: 'Skills', value: user?.skills?.length?.toString() || '0', color: 'neon-pink' },
              ].map((stat, index) => (
                <motion.div
                  key={stat.label}
                  whileHover={{ y: -2 }}
                  className="bg-dark-800/50 border border-dark-700 rounded-xl p-4 text-center"
                >
                  <div className={`text-2xl font-bold text-${stat.color} mb-1`}>{stat.value}</div>
                  <div className="text-gray-400 text-sm">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>

            {/* Empty State */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-center py-16"
            >
              <div className="bg-dark-800/50 border border-dark-700 rounded-xl p-8 max-w-md mx-auto">
                <div className="w-16 h-16 bg-neon-blue/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MessageCircle className="w-8 h-8 text-neon-blue" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">No posts yet</h3>
                <p className="text-gray-400 mb-6">
                  Start following other developers and designers to see their posts in your feed.
                </p>
                <div className="space-y-3">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="w-full px-6 py-3 bg-gradient-to-r from-neon-blue to-neon-purple rounded-xl font-semibold text-white"
                  >
                    Explore Community
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="w-full px-6 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white hover:border-neon-blue transition-colors"
                  >
                    Create Your First Post
                  </motion.button>
                </div>
              </div>
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Dashboard;
