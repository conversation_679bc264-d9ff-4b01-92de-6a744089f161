import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import Sidebar from '../components/Layout/Sidebar';
import TopBar from '../components/Layout/TopBar';
import { ArrowLeft, Upload, Plus, X, Link as LinkIcon, Github, Globe, Figma } from 'lucide-react';

const CreateProjectPage: React.FC = () => {
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'Web Development',
    tags: [] as string[],
    githubUrl: '',
    liveUrl: '',
    figmaUrl: '',
    isPublic: true,
  });
  const [newTag, setNewTag] = useState('');
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);

  const categories = [
    'Web Development',
    'Mobile Apps',
    'UI/UX Design',
    'Graphic Design',
    'Data Science',
    'Machine Learning',
    'DevOps',
    'Game Development',
    'Desktop Apps',
    'Other'
  ];

  const popularTags = [
    'React', 'TypeScript', 'JavaScript', 'Python', 'Node.js', 'Vue.js', 
    'Angular', 'CSS', 'HTML', 'Figma', 'Adobe XD', 'Sketch',
    'UI/UX', 'Frontend', 'Backend', 'Full Stack', 'Mobile', 'Design'
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const addTag = (tag: string) => {
    if (tag.trim() && !formData.tags.includes(tag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.target === document.activeElement) {
      e.preventDefault();
      addTag(newTag);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would submit to an API
    console.log('Project data:', formData);
    navigate('/dashboard');
  };

  const mockUploadImage = () => {
    const newImage = `https://images.unsplash.com/photo-${1500000000000 + Math.floor(Math.random() * 100000000)}?w=400&h=300&fit=crop`;
    setUploadedImages(prev => [...prev, newImage]);
  };

  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="flex h-screen bg-dark-950">
      <Sidebar />
      
      <div className="flex-1 flex flex-col ml-64">
        <TopBar onToggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        
        <main className="flex-1 overflow-y-auto pt-16">
          <div className="max-w-4xl mx-auto px-6 py-8">
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-center mb-8"
            >
              <motion.button
                whileHover={{ x: -2 }}
                onClick={() => navigate('/dashboard')}
                className="flex items-center text-neon-blue hover:text-white mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Back to Dashboard
              </motion.button>
              <h1 className="text-3xl font-bold text-white">Create New Project</h1>
            </motion.div>

            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Basic Information */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-dark-800/50 border border-dark-700 rounded-xl p-6"
              >
                <h2 className="text-xl font-semibold text-white mb-6">Project Details</h2>
                
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Project Title *
                    </label>
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white focus:outline-none focus:border-neon-blue transition-colors"
                      placeholder="Enter your project title"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Description *
                    </label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      required
                      rows={5}
                      className="w-full px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white focus:outline-none focus:border-neon-blue transition-colors resize-none"
                      placeholder="Describe your project, what it does, and what technologies you used..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Category *
                    </label>
                    <select
                      name="category"
                      value={formData.category}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white focus:outline-none focus:border-neon-blue transition-colors"
                    >
                      {categories.map(category => (
                        <option key={category} value={category}>{category}</option>
                      ))}
                    </select>
                  </div>
                </div>
              </motion.div>

              {/* Media Upload */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="bg-dark-800/50 border border-dark-700 rounded-xl p-6"
              >
                <h2 className="text-xl font-semibold text-white mb-6">Project Images</h2>
                
                {/* Upload Area */}
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  onClick={mockUploadImage}
                  className="border-2 border-dashed border-dark-600 rounded-xl p-8 text-center cursor-pointer hover:border-neon-blue transition-colors mb-6"
                >
                  <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-400 mb-2">Click to upload images</p>
                  <p className="text-gray-500 text-sm">PNG, JPG, GIF up to 10MB each</p>
                </motion.div>

                {/* Uploaded Images */}
                {uploadedImages.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {uploadedImages.map((image, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="relative group"
                      >
                        <img
                          src={image}
                          alt={`Upload ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg"
                        />
                        <motion.button
                          type="button"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={() => removeImage(index)}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="w-3 h-3" />
                        </motion.button>
                      </motion.div>
                    ))}
                  </div>
                )}
              </motion.div>

              {/* Project Links */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="bg-dark-800/50 border border-dark-700 rounded-xl p-6"
              >
                <h2 className="text-xl font-semibold text-white mb-6">Project Links</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      <Github className="w-4 h-4 inline mr-2" />
                      GitHub Repository
                    </label>
                    <input
                      type="url"
                      name="githubUrl"
                      value={formData.githubUrl}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white focus:outline-none focus:border-neon-blue transition-colors"
                      placeholder="https://github.com/..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      <Globe className="w-4 h-4 inline mr-2" />
                      Live Demo
                    </label>
                    <input
                      type="url"
                      name="liveUrl"
                      value={formData.liveUrl}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white focus:outline-none focus:border-neon-blue transition-colors"
                      placeholder="https://yourproject.com"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      <Figma className="w-4 h-4 inline mr-2" />
                      Figma Design
                    </label>
                    <input
                      type="url"
                      name="figmaUrl"
                      value={formData.figmaUrl}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white focus:outline-none focus:border-neon-blue transition-colors"
                      placeholder="https://figma.com/..."
                    />
                  </div>
                </div>
              </motion.div>

              {/* Tags */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="bg-dark-800/50 border border-dark-700 rounded-xl p-6"
              >
                <h2 className="text-xl font-semibold text-white mb-6">Tags & Technologies</h2>
                
                {/* Current Tags */}
                {formData.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mb-4">
                    {formData.tags.map(tag => (
                      <span
                        key={tag}
                        className="flex items-center space-x-2 px-3 py-1 bg-neon-blue/20 text-neon-blue rounded-lg"
                      >
                        <span>{tag}</span>
                        <motion.button
                          type="button"
                          whileHover={{ scale: 1.2 }}
                          whileTap={{ scale: 0.8 }}
                          onClick={() => removeTag(tag)}
                          className="text-neon-blue hover:text-white"
                        >
                          <X className="w-3 h-3" />
                        </motion.button>
                      </span>
                    ))}
                  </div>
                )}

                {/* Add Tag Input */}
                <div className="flex space-x-2 mb-4">
                  <input
                    type="text"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="flex-1 px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white focus:outline-none focus:border-neon-blue transition-colors"
                    placeholder="Add a tag (e.g., React, Figma, Python)"
                  />
                  <motion.button
                    type="button"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => addTag(newTag)}
                    className="px-4 py-3 bg-neon-blue text-white rounded-xl flex items-center"
                  >
                    <Plus className="w-4 h-4" />
                  </motion.button>
                </div>

                {/* Popular Tags */}
                <div>
                  <p className="text-sm text-gray-400 mb-3">Popular tags:</p>
                  <div className="flex flex-wrap gap-2">
                    {popularTags.map(tag => (
                      <motion.button
                        key={tag}
                        type="button"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => addTag(tag)}
                        disabled={formData.tags.includes(tag)}
                        className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                          formData.tags.includes(tag)
                            ? 'bg-dark-600 text-gray-500 cursor-not-allowed'
                            : 'bg-dark-700 text-gray-300 hover:text-neon-blue hover:bg-dark-600'
                        }`}
                      >
                        {tag}
                      </motion.button>
                    ))}
                  </div>
                </div>
              </motion.div>

              {/* Privacy & Publishing */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="bg-dark-800/50 border border-dark-700 rounded-xl p-6"
              >
                <h2 className="text-xl font-semibold text-white mb-6">Privacy Settings</h2>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-white font-medium mb-2">Public Project</h3>
                    <p className="text-gray-400 text-sm">
                      Make this project visible to everyone in the community
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.isPublic}
                      onChange={(e) => setFormData(prev => ({ ...prev, isPublic: e.target.checked }))}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-dark-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-neon-blue"></div>
                  </label>
                </div>
              </motion.div>

              {/* Action Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4"
              >
                <motion.button
                  type="submit"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="flex-1 py-3 bg-gradient-to-r from-neon-blue to-neon-purple rounded-xl font-semibold text-white"
                >
                  Publish Project
                </motion.button>
                <motion.button
                  type="button"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="flex-1 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white hover:border-gray-500 transition-colors"
                >
                  Save as Draft
                </motion.button>
                <motion.button
                  type="button"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => navigate('/dashboard')}
                  className="px-8 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white hover:border-gray-500 transition-colors"
                >
                  Cancel
                </motion.button>
              </motion.div>
            </form>
          </div>
        </main>
      </div>
    </div>
  );
};

export default CreateProjectPage;
