import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useParams, useNavigate } from 'react-router-dom';
import Sidebar from '../components/Layout/Sidebar';
import TopBar from '../components/Layout/TopBar';
import { 
  ArrowLeft, 
  Heart, 
  MessageCircle, 
  Share, 
  Bookmark, 
  Star, 
  Eye, 
  Github, 
  Globe, 
  Figma,
  Calendar,
  Code,
  Palette,
  Send,
  MoreHorizontal
} from 'lucide-react';

const ProjectDetailPage: React.FC = () => {
  const { projectId } = useParams();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isStarred, setIsStarred] = useState(false);
  const [comment, setComment] = useState('');
  const [activeImageIndex, setActiveImageIndex] = useState(0);

  // Project not found - real projects will be created by users
  const project = null;

  // Empty comments - real comments will be added by users
  const comments: any[] = [];

  const handleComment = () => {
    if (comment.trim()) {
      // Add comment logic here
      setComment('');
    }
  };

  return (
    <div className="flex h-screen bg-dark-950">
      <Sidebar />
      
      <div className="flex-1 flex flex-col ml-64">
        <TopBar onToggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        
        <main className="flex-1 overflow-y-auto pt-16">
          <div className="max-w-6xl mx-auto px-6 py-8">
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-center mb-8"
            >
              <motion.button
                whileHover={{ x: -2 }}
                onClick={() => navigate('/dashboard')}
                className="flex items-center text-neon-blue hover:text-white mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Back to Dashboard
              </motion.button>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Main Content */}
              <div className="lg:col-span-2 space-y-8">
                {/* Project Images */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-dark-800/50 border border-dark-700 rounded-xl overflow-hidden"
                >
                  <div className="aspect-video relative">
                    <img
                      src={project.images[activeImageIndex]}
                      alt={project.title}
                      className="w-full h-full object-cover"
                    />
                    {project.images.length > 1 && (
                      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                        {project.images.map((_, index) => (
                          <motion.button
                            key={index}
                            whileHover={{ scale: 1.2 }}
                            onClick={() => setActiveImageIndex(index)}
                            className={`w-3 h-3 rounded-full ${
                              index === activeImageIndex ? 'bg-neon-blue' : 'bg-white/50'
                            }`}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                  
                  {project.images.length > 1 && (
                    <div className="p-4 flex space-x-2 overflow-x-auto">
                      {project.images.map((image, index) => (
                        <motion.img
                          key={index}
                          whileHover={{ scale: 1.05 }}
                          src={image}
                          alt={`${project.title} ${index + 1}`}
                          onClick={() => setActiveImageIndex(index)}
                          className={`w-20 h-16 object-cover rounded-lg cursor-pointer ${
                            index === activeImageIndex ? 'ring-2 ring-neon-blue' : ''
                          }`}
                        />
                      ))}
                    </div>
                  )}
                </motion.div>

                {/* Project Info */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                  className="bg-dark-800/50 border border-dark-700 rounded-xl p-6"
                >
                  {/* Author */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-3">
                      <img
                        src={project.author.avatar}
                        alt={project.author.name}
                        className="w-12 h-12 rounded-full"
                      />
                      <div>
                        <h3 className="font-semibold text-white">{project.author.name}</h3>
                        <div className="flex items-center space-x-2">
                          <span className="text-gray-400 text-sm">@{project.author.username}</span>
                          <span className="px-2 py-1 bg-neon-blue/20 text-neon-blue text-xs rounded-full">
                            <Code className="w-3 h-3 inline mr-1" />
                            {project.author.role}
                          </span>
                        </div>
                      </div>
                    </div>
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="p-2 text-gray-400 hover:text-white hover:bg-dark-700 rounded-lg"
                    >
                      <MoreHorizontal className="w-5 h-5" />
                    </motion.button>
                  </div>

                  {/* Title and Description */}
                  <h1 className="text-3xl font-bold text-white mb-4">{project.title}</h1>
                  <p className="text-gray-300 leading-relaxed mb-6">{project.description}</p>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-6">
                    {project.tags.map(tag => (
                      <span
                        key={tag}
                        className="px-3 py-1 bg-dark-700 text-neon-blue text-sm rounded-lg hover:bg-dark-600 cursor-pointer"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>

                  {/* Project Links */}
                  <div className="flex flex-wrap gap-4 mb-6">
                    {project.githubUrl && (
                      <motion.a
                        href={project.githubUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        whileHover={{ scale: 1.05 }}
                        className="flex items-center space-x-2 px-4 py-2 bg-dark-700 text-white rounded-lg hover:bg-dark-600 transition-colors"
                      >
                        <Github className="w-4 h-4" />
                        <span>View Code</span>
                      </motion.a>
                    )}
                    {project.liveUrl && (
                      <motion.a
                        href={project.liveUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        whileHover={{ scale: 1.05 }}
                        className="flex items-center space-x-2 px-4 py-2 bg-neon-blue text-white rounded-lg hover:bg-neon-blue/80 transition-colors"
                      >
                        <Globe className="w-4 h-4" />
                        <span>Live Demo</span>
                      </motion.a>
                    )}
                    {project.figmaUrl && (
                      <motion.a
                        href={project.figmaUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        whileHover={{ scale: 1.05 }}
                        className="flex items-center space-x-2 px-4 py-2 bg-neon-purple text-white rounded-lg hover:bg-neon-purple/80 transition-colors"
                      >
                        <Figma className="w-4 h-4" />
                        <span>Design</span>
                      </motion.a>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between pt-6 border-t border-dark-700">
                    <div className="flex items-center space-x-6">
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => setIsLiked(!isLiked)}
                        className={`flex items-center space-x-2 ${
                          isLiked ? 'text-neon-pink' : 'text-gray-400 hover:text-neon-pink'
                        }`}
                      >
                        <Heart className={`w-6 h-6 ${isLiked ? 'fill-current' : ''}`} />
                        <span>{project.likes + (isLiked ? 1 : 0)}</span>
                      </motion.button>

                      <div className="flex items-center space-x-2 text-gray-400">
                        <MessageCircle className="w-6 h-6" />
                        <span>{project.comments}</span>
                      </div>

                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        className="flex items-center space-x-2 text-gray-400 hover:text-neon-teal"
                      >
                        <Share className="w-6 h-6" />
                        <span>{project.shares}</span>
                      </motion.button>

                      <div className="flex items-center space-x-2 text-gray-400">
                        <Eye className="w-6 h-6" />
                        <span>{project.views}</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => setIsStarred(!isStarred)}
                        className={`p-3 rounded-lg ${
                          isStarred 
                            ? 'text-yellow-400 bg-yellow-400/20' 
                            : 'text-gray-400 hover:text-yellow-400 hover:bg-dark-700'
                        }`}
                      >
                        <Star className={`w-6 h-6 ${isStarred ? 'fill-current' : ''}`} />
                      </motion.button>

                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => setIsBookmarked(!isBookmarked)}
                        className={`p-3 rounded-lg ${
                          isBookmarked 
                            ? 'text-neon-blue bg-neon-blue/20' 
                            : 'text-gray-400 hover:text-neon-blue hover:bg-dark-700'
                        }`}
                      >
                        <Bookmark className={`w-6 h-6 ${isBookmarked ? 'fill-current' : ''}`} />
                      </motion.button>
                    </div>
                  </div>
                </motion.div>

                {/* Comments Section */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="bg-dark-800/50 border border-dark-700 rounded-xl p-6"
                >
                  <h2 className="text-xl font-semibold text-white mb-6">
                    Comments ({comments.length})
                  </h2>

                  {/* Add Comment */}
                  <div className="mb-8">
                    <div className="flex space-x-3">
                      <img
                        src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face"
                        alt="Your avatar"
                        className="w-10 h-10 rounded-full"
                      />
                      <div className="flex-1">
                        <textarea
                          value={comment}
                          onChange={(e) => setComment(e.target.value)}
                          placeholder="Add a comment..."
                          className="w-full px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-neon-blue transition-colors resize-none"
                          rows={3}
                        />
                        <div className="flex justify-end mt-3">
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={handleComment}
                            disabled={!comment.trim()}
                            className="flex items-center space-x-2 px-4 py-2 bg-neon-blue text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <Send className="w-4 h-4" />
                            <span>Comment</span>
                          </motion.button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Comments List */}
                  <div className="space-y-6">
                    {comments.map((comment, index) => (
                      <motion.div
                        key={comment.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05 }}
                        className="flex space-x-3"
                      >
                        <img
                          src={comment.author.avatar}
                          alt={comment.author.name}
                          className="w-10 h-10 rounded-full"
                        />
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="font-semibold text-white text-sm">{comment.author.name}</span>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              comment.author.role === 'Developer' 
                                ? 'bg-neon-blue/20 text-neon-blue' 
                                : 'bg-neon-purple/20 text-neon-purple'
                            }`}>
                              {comment.author.role === 'Developer' ? <Code className="w-3 h-3 inline mr-1" /> : <Palette className="w-3 h-3 inline mr-1" />}
                              {comment.author.role}
                            </span>
                            <span className="text-gray-500 text-xs">{comment.timeAgo}</span>
                          </div>
                          <p className="text-gray-300 text-sm mb-2">{comment.content}</p>
                          <div className="flex items-center space-x-4">
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              className={`flex items-center space-x-1 text-xs ${
                                comment.isLiked ? 'text-neon-pink' : 'text-gray-400 hover:text-neon-pink'
                              }`}
                            >
                              <Heart className={`w-3 h-3 ${comment.isLiked ? 'fill-current' : ''}`} />
                              <span>{comment.likes}</span>
                            </motion.button>
                            <button className="text-gray-400 hover:text-white text-xs">
                              Reply
                            </button>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Project Stats */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                  className="bg-dark-800/50 border border-dark-700 rounded-xl p-6"
                >
                  <h3 className="text-lg font-semibold text-white mb-4">Project Stats</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Heart className="w-4 h-4 text-neon-pink" />
                        <span className="text-gray-300">Likes</span>
                      </div>
                      <span className="text-white font-semibold">{project.likes}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Star className="w-4 h-4 text-yellow-400" />
                        <span className="text-gray-300">Stars</span>
                      </div>
                      <span className="text-white font-semibold">{project.stars}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Eye className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-300">Views</span>
                      </div>
                      <span className="text-white font-semibold">{project.views}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <MessageCircle className="w-4 h-4 text-neon-blue" />
                        <span className="text-gray-300">Comments</span>
                      </div>
                      <span className="text-white font-semibold">{project.comments}</span>
                    </div>
                  </div>
                </motion.div>

                {/* Project Details */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 }}
                  className="bg-dark-800/50 border border-dark-700 rounded-xl p-6"
                >
                  <h3 className="text-lg font-semibold text-white mb-4">Project Details</h3>
                  <div className="space-y-4">
                    <div>
                      <span className="text-gray-400 text-sm">Category</span>
                      <p className="text-white">{project.category}</p>
                    </div>
                    <div>
                      <span className="text-gray-400 text-sm">Created</span>
                      <p className="text-white flex items-center">
                        <Calendar className="w-4 h-4 mr-2" />
                        {new Date(project.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <span className="text-gray-400 text-sm">Last Updated</span>
                      <p className="text-white flex items-center">
                        <Calendar className="w-4 h-4 mr-2" />
                        {new Date(project.updatedAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </motion.div>

                {/* Related Projects */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.5 }}
                  className="bg-dark-800/50 border border-dark-700 rounded-xl p-6"
                >
                  <h3 className="text-lg font-semibold text-white mb-4">Similar Projects</h3>
                  <div className="space-y-4">
                    {Array.from({ length: 3 }, (_, index) => (
                      <motion.div
                        key={index}
                        whileHover={{ x: 5 }}
                        className="flex space-x-3 cursor-pointer"
                      >
                        <img
                          src={`https://images.unsplash.com/photo-${1500000000000 + Math.floor(Math.random() * *********)}?w=100&h=80&fit=crop`}
                          alt="Project thumbnail"
                          className="w-16 h-12 object-cover rounded-lg"
                        />
                        <div className="flex-1">
                          <h4 className="text-white text-sm font-medium truncate">
                            {faker.company.catchPhrase()}
                          </h4>
                          <p className="text-gray-400 text-xs">
                            by {faker.person.firstName()}
                          </p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Heart className="w-3 h-3 text-neon-pink" />
                            <span className="text-xs text-gray-400">
                              {Math.floor(Math.random() * 100) + 10}
                            </span>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default ProjectDetailPage;
