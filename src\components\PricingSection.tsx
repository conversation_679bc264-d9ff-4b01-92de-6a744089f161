import React from 'react';
import { motion } from 'framer-motion';

const tiers = [
  {
    name: 'Free',
    price: '$0',
    features: ['Public projects', 'Community access', 'Basic profile'],
    cta: 'Get Started',
    highlight: false,
  },
  {
    name: 'Pro',
    price: '$9/mo',
    features: ['Private projects', 'Advanced analytics', 'Priority support'],
    cta: 'Upgrade',
    highlight: true,
  },
  {
    name: 'Team',
    price: '$29/mo',
    features: ['Team collaboration', 'Role permissions', 'Workspace insights'],
    cta: 'Start Team',
    highlight: false,
  },
];

const PricingSection: React.FC = () => {
  return (
    <section id="pricing" className="py-20 px-6 scroll-mt-24">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-6xl font-bold mb-4">Transparent Pricing</h2>
          <p className="text-gray-400">Choose a plan that fits your needs. Upgrade anytime.</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {tiers.map((tier) => (
            <motion.div
              key={tier.name}
              whileHover={{ y: -6 }}
              className={`p-6 rounded-2xl border ${
                tier.highlight ? 'border-neon-purple bg-dark-800/50' : 'border-dark-700 bg-dark-900/40'
              }`}
            >
              <h3 className="text-2xl font-semibold mb-2">{tier.name}</h3>
              <div className="text-3xl font-bold mb-4">{tier.price}</div>
              <ul className="text-gray-300 space-y-2 mb-6">
                {tier.features.map((f) => (
                  <li key={f}>• {f}</li>
                ))}
              </ul>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="w-full px-6 py-3 rounded-lg bg-gradient-to-r from-neon-blue to-neon-purple"
              >
                {tier.cta}
              </motion.button>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PricingSection;

