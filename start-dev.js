// Simple script to start the development server
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';

// Get current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Get the path to node_modules/.bin/vite
const viteBinPath = resolve(__dirname, 'node_modules', '.bin', 'vite');

console.log('Starting Vite server from:', viteBinPath);

// Start the development server
const viteProcess = spawn(viteBinPath, [], {
  stdio: 'inherit',
  shell: true
});

viteProcess.on('error', (err) => {
  console.error('Failed to start Vite server:', err);
});

viteProcess.on('close', (code) => {
  console.log(`Vite server process exited with code ${code}`);
});