import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Sidebar from '../components/Layout/Sidebar';
import TopBar from '../components/Layout/TopBar';
import { User, Bell, Shield, CreditCard, Sun, Moon, Trash2 } from 'lucide-react';

type SettingsTab = 'account' | 'notifications' | 'security' | 'billing' | 'appearance';

const SettingsPage: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<SettingsTab>('account');

  const tabs = [
    { id: 'account', label: 'Account', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'billing', label: 'Billing', icon: CreditCard },
    { id: 'appearance', label: 'Appearance', icon: Sun },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'account':
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
              <input type="email" defaultValue="<EMAIL>" className="w-full md:w-1/2 px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Username</label>
              <div className="flex items-center">
                <span className="px-4 py-3 bg-dark-800 border border-dark-600 rounded-l-xl text-gray-400">devhub.com/</span>
                <input type="text" defaultValue="alex_rodriguez" className="w-full md:w-1/3 px-4 py-3 bg-dark-700 border-t border-b border-r border-dark-600 rounded-r-xl" />
              </div>
            </div>
            <div className="pt-6 border-t border-dark-700">
              <h3 className="text-lg font-semibold text-red-400 mb-2">Delete Account</h3>
              <p className="text-gray-400 mb-4">Permanently delete your account and all of your content.</p>
              <motion.button whileHover={{scale: 1.02}} className="flex items-center space-x-2 px-4 py-2 bg-red-500/20 border border-red-500/50 text-red-400 rounded-lg">
                <Trash2 className="w-4 h-4" />
                <span>Delete my account</span>
              </motion.button>
            </div>
          </div>
        );
      case 'notifications':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white">Email Notifications</h3>
            <div className="flex items-center justify-between">
              <p>New followers</p>
              <input type="checkbox" className="toggle-switch" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <p>Project likes and comments</p>
              <input type="checkbox" className="toggle-switch" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <p>Mentions</p>
              <input type="checkbox" className="toggle-switch" defaultChecked />
            </div>
          </div>
        );
      case 'security':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white">Change Password</h3>
            <input type="password" placeholder="Current Password" className="w-full md:w-1/2 px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl" />
            <input type="password" placeholder="New Password" className="w-full md:w-1/2 px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl" />
            <h3 className="text-lg font-semibold text-white pt-6 border-t border-dark-700">Two-Factor Authentication</h3>
            <p className="text-gray-400">Add an extra layer of security to your account.</p>
            <motion.button whileHover={{scale: 1.02}} className="px-4 py-2 bg-neon-blue text-white rounded-lg">Enable 2FA</motion.button>
          </div>
        );
      case 'appearance':
        return (
            <div className="space-y-6">
                <h3 className="text-lg font-semibold text-white">Theme</h3>
                <p className="text-gray-400">Select your preferred theme for the application.</p>
                <div className="flex space-x-4">
                    <div className="flex-1 p-4 border-2 border-neon-blue rounded-xl bg-dark-800 cursor-pointer">
                        <Moon className="w-6 h-6 text-neon-blue mb-2" />
                        <h4 className="font-semibold text-white">Dark Mode</h4>
                    </div>
                    <div className="flex-1 p-4 border border-dark-600 rounded-xl bg-dark-700 cursor-pointer opacity-50">
                        <Sun className="w-6 h-6 text-gray-400 mb-2" />
                        <h4 className="font-semibold text-gray-400">Light Mode (Coming Soon)</h4>
                    </div>
                </div>
            </div>
        );
      default:
        return <p>Coming soon.</p>;
    }
  };

  return (
    <div className="flex h-screen bg-dark-950">
      <Sidebar />
      <div className="flex-1 flex flex-col ml-64">
        <TopBar onToggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto pt-16">
          <div className="max-w-6xl mx-auto px-6 py-8">
            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
              <h1 className="text-3xl font-bold text-white mb-8">Settings</h1>
            </motion.div>
            <div className="flex flex-col md:flex-row gap-8">
              <motion.div initial={{ opacity: 0, x: -20 }} animate={{ opacity: 1, x: 0 }} transition={{ delay: 0.1 }} className="md:w-1/4">
                <nav className="space-y-2">
                  {tabs.map(tab => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id as SettingsTab)}
                      className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-colors ${
                        activeTab === tab.id
                          ? 'bg-neon-blue/20 text-neon-blue'
                          : 'text-gray-400 hover:text-white hover:bg-dark-800'
                      }`}
                    >
                      <tab.icon className="w-5 h-5" />
                      <span>{tab.label}</span>
                    </button>
                  ))}
                </nav>
              </motion.div>
              <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }} className="flex-1 bg-dark-800/50 border border-dark-700 rounded-xl p-8">
                {renderContent()}
                <div className="mt-8 pt-6 border-t border-dark-700">
                    <motion.button whileHover={{scale: 1.02}} className="px-6 py-3 bg-gradient-to-r from-neon-blue to-neon-purple rounded-xl font-semibold">
                        Save Changes
                    </motion.button>
                </div>
              </motion.div>
            </div>
          </div>
        </main>
      </div>
      <style>{`
        .toggle-switch {
          position: relative;
          display: inline-block;
          width: 44px;
          height: 24px;
          background-color: #475569;
          border-radius: 9999px;
          transition: background-color 0.2s;
          appearance: none;
          cursor: pointer;
        }
        .toggle-switch:checked {
          background-color: #00d4ff;
        }
        .toggle-switch::before {
          content: '';
          position: absolute;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background-color: white;
          top: 2px;
          left: 2px;
          transition: transform 0.2s;
        }
        .toggle-switch:checked::before {
          transform: translateX(20px);
        }
      `}</style>
    </div>
  );
};

export default SettingsPage;
