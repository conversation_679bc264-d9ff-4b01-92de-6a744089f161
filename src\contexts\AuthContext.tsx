import React, { createContext, useContext, useState, useEffect } from 'react';

interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  role: 'Developer' | 'Designer' | 'Both';
  bio: string;
  skills: string[];
  location: string;
  website?: string;
  github?: string;
  twitter?: string;
  linkedin?: string;
  joinedDate: string;
  followers: number;
  following: number;
  projects: number;
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  register: (userData: RegisterData) => Promise<boolean>;
  logout: () => void;
  updateProfile: (data: Partial<User>) => void;
  isLoading: boolean;
}

interface RegisterData {
  name: string;
  email: string;
  password: string;
  role: 'Developer' | 'Designer' | 'Both';
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Load user session from localStorage
    const savedUser = localStorage.getItem('devhub_user');
    if (savedUser) {
      try {
        setUser(JSON.parse(savedUser));
      } catch (error) {
        console.error('Error parsing saved user data:', error);
        localStorage.removeItem('devhub_user');
      }
    }
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true);

    // Basic validation
    if (!email || !password) {
      setIsLoading(false);
      return false;
    }

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // For now, accept any email/password combination
    // In a real app, this would make an API call to authenticate
    const userData: User = {
      id: Date.now().toString(), // Simple ID generation
      name: email.split('@')[0], // Use email prefix as name
      email: email,
      avatar: '', // No avatar initially
      role: 'Developer', // Default role
      bio: '',
      skills: [],
      location: '',
      website: '',
      github: '',
      twitter: '',
      linkedin: '',
      joinedDate: new Date().toISOString().split('T')[0],
      followers: 0,
      following: 0,
      projects: 0
    };

    setUser(userData);
    localStorage.setItem('devhub_user', JSON.stringify(userData));
    setIsLoading(false);
    return true;
  };

  const register = async (userData: RegisterData): Promise<boolean> => {
    setIsLoading(true);

    // Basic validation
    if (!userData.name || !userData.email || !userData.password) {
      setIsLoading(false);
      return false;
    }

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Create new user with provided data
    const newUser: User = {
      id: Date.now().toString(), // Simple ID generation
      name: userData.name,
      email: userData.email,
      avatar: '', // No avatar initially
      role: userData.role,
      bio: '',
      skills: [],
      location: '',
      website: '',
      github: '',
      twitter: '',
      linkedin: '',
      joinedDate: new Date().toISOString().split('T')[0],
      followers: 0,
      following: 0,
      projects: 0
    };

    setUser(newUser);
    localStorage.setItem('devhub_user', JSON.stringify(newUser));
    setIsLoading(false);
    return true;
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('devhub_user');
  };

  const updateProfile = (data: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...data };
      setUser(updatedUser);
      localStorage.setItem('devhub_user', JSON.stringify(updatedUser));
    }
  };

  const value = {
    user,
    login,
    register,
    logout,
    updateProfile,
    isLoading
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
