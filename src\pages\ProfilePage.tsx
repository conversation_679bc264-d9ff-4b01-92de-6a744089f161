import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { usePara<PERSON>, Link } from 'react-router-dom';
import Sidebar from '../components/Layout/Sidebar';
import TopBar from '../components/Layout/TopBar';
import { useAuth } from '../contexts/AuthContext';
import { 
  MapPin, 
  Link as LinkIcon, 
  Calendar, 
  Edit, 
  Heart, 
  Star, 
  Eye, 
  MessageCircle,
  Github,
  Twitter,
  Linkedin,
  Globe,
  Users,
  Code,
  Palette
} from 'lucide-react';

const ProfilePage: React.FC = () => {
  const { userId } = useParams();
  const { user: currentUser } = useAuth();
  const [activeTab, setActiveTab] = useState('projects');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Determine if viewing own profile
  const isOwnProfile = !userId || userId === currentUser?.id;
  const profileUser = isOwnProfile ? currentUser : null;

  // Empty arrays for projects and posts (will be populated when user creates content)
  const projects: any[] = [];
  const posts: any[] = [];

  const tabs = [
    { id: 'projects', label: 'Projects', count: projects.length },
    { id: 'posts', label: 'Posts', count: posts.length },
    { id: 'liked', label: 'Liked', count: 0 },
    { id: 'followers', label: 'Followers', count: profileUser?.followers || 0 },
  ];

  if (!profileUser) {
    return <div>User not found</div>;
  }

  return (
    <div className="flex h-screen bg-dark-950">
      <Sidebar />
      
      <div className="flex-1 flex flex-col ml-64">
        <TopBar onToggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        
        <main className="flex-1 overflow-y-auto pt-16">
          <div className="max-w-6xl mx-auto px-6 py-8">
            {/* Profile Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-dark-800/50 border border-dark-700 rounded-2xl p-8 mb-8"
            >
              {/* Cover Photo Area */}
              <div className="relative -m-8 mb-4 h-48 bg-gradient-to-r from-neon-blue/20 via-neon-purple/20 to-neon-teal/20 rounded-t-2xl">
                <div className="absolute inset-0 bg-gradient-to-br from-transparent to-dark-900/50" />
              </div>

              <div className="relative">
                {/* Avatar and Basic Info */}
                <div className="flex flex-col md:flex-row items-start md:items-end space-y-4 md:space-y-0 md:space-x-6">
                  <motion.img
                    whileHover={{ scale: 1.05 }}
                    src={profileUser.avatar}
                    alt={profileUser.name}
                    className="w-32 h-32 rounded-2xl border-4 border-dark-800 bg-dark-800 -mt-16"
                  />
                  
                  <div className="flex-1">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                      <div>
                        <h1 className="text-3xl font-bold text-white mb-2">{profileUser.name}</h1>
                        <div className="flex items-center space-x-4 text-gray-400 mb-4">
                          <span className={`px-3 py-1 rounded-full text-sm ${
                            profileUser.role === 'Developer' 
                              ? 'bg-neon-blue/20 text-neon-blue' 
                              : profileUser.role === 'Designer'
                              ? 'bg-neon-purple/20 text-neon-purple'
                              : 'bg-neon-teal/20 text-neon-teal'
                          }`}>
                            {profileUser.role === 'Developer' ? <Code className="w-4 h-4 inline mr-1" /> : <Palette className="w-4 h-4 inline mr-1" />}
                            {profileUser.role}
                          </span>
                          {profileUser.location && (
                            <span className="flex items-center">
                              <MapPin className="w-4 h-4 mr-1" />
                              {profileUser.location}
                            </span>
                          )}
                          <span className="flex items-center">
                            <Calendar className="w-4 h-4 mr-1" />
                            Joined {new Date(profileUser.joinedDate).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                          </span>
                        </div>
                      </div>
                      
                      {isOwnProfile ? (
                        <Link to="/edit-profile">
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className="flex items-center space-x-2 px-6 py-2 bg-dark-700 border border-dark-600 rounded-xl text-white hover:border-neon-blue transition-colors"
                          >
                            <Edit className="w-4 h-4" />
                            <span>Edit Profile</span>
                          </motion.button>
                        </Link>
                      ) : (
                        <div className="flex space-x-3">
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className="px-6 py-2 bg-gradient-to-r from-neon-blue to-neon-purple rounded-xl font-semibold"
                          >
                            Follow
                          </motion.button>
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className="px-6 py-2 bg-dark-700 border border-dark-600 rounded-xl text-white hover:border-neon-blue transition-colors"
                          >
                            Message
                          </motion.button>
                        </div>
                      )}
                    </div>

                    {/* Bio */}
                    {profileUser.bio && (
                      <p className="text-gray-300 mb-4 max-w-2xl">{profileUser.bio}</p>
                    )}

                    {/* Skills */}
                    {profileUser.skills && profileUser.skills.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-4">
                        {profileUser.skills.map(skill => (
                          <span
                            key={skill}
                            className="px-3 py-1 bg-dark-700 text-neon-blue text-sm rounded-lg"
                          >
                            {skill}
                          </span>
                        ))}
                      </div>
                    )}

                    {/* Social Links */}
                    <div className="flex items-center space-x-4">
                      {profileUser.website && (
                        <a href={profileUser.website} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-neon-blue">
                          <Globe className="w-5 h-5" />
                        </a>
                      )}
                      {profileUser.github && (
                        <a href={`https://github.com/${profileUser.github}`} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-neon-blue">
                          <Github className="w-5 h-5" />
                        </a>
                      )}
                      {profileUser.twitter && (
                        <a href={`https://twitter.com/${profileUser.twitter}`} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-neon-blue">
                          <Twitter className="w-5 h-5" />
                        </a>
                      )}
                      {profileUser.linkedin && (
                        <a href={`https://linkedin.com/in/${profileUser.linkedin}`} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-neon-blue">
                          <Linkedin className="w-5 h-5" />
                        </a>
                      )}
                    </div>
                  </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-3 gap-8 mt-8 pt-8 border-t border-dark-700">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">{profileUser.followers?.toLocaleString()}</div>
                    <div className="text-gray-400">Followers</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">{profileUser.following?.toLocaleString()}</div>
                    <div className="text-gray-400">Following</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">{profileUser.projects}</div>
                    <div className="text-gray-400">Projects</div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Tabs */}
            <div className="flex space-x-1 mb-8">
              {tabs.map(tab => (
                <motion.button
                  key={tab.id}
                  whileHover={{ y: -2 }}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-6 py-3 rounded-xl font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-neon-blue/20 text-neon-blue border border-neon-blue/30'
                      : 'bg-dark-800/50 text-gray-400 border border-dark-700 hover:text-white'
                  }`}
                >
                  {tab.label} ({tab.count})
                </motion.button>
              ))}
            </div>

            {/* Tab Content */}
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {activeTab === 'projects' && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {projects.map((project, index) => (
                    <motion.div
                      key={project.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      whileHover={{ y: -5 }}
                      className="bg-dark-800/50 border border-dark-700 rounded-xl overflow-hidden hover:border-dark-600 transition-colors"
                    >
                      <div className="aspect-video bg-gradient-to-br from-neon-blue/10 to-neon-purple/10 relative overflow-hidden">
                        <img
                          src={project.image}
                          alt={project.title}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="p-6">
                        <h3 className="font-semibold text-white mb-2 truncate">{project.title}</h3>
                        <p className="text-gray-400 text-sm mb-4 line-clamp-2">{project.description}</p>
                        <div className="flex flex-wrap gap-2 mb-4">
                          {project.tags.map(tag => (
                            <span
                              key={tag}
                              className="px-2 py-1 bg-dark-700 text-neon-blue text-xs rounded-lg"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                        <div className="flex items-center justify-between text-gray-400 text-sm">
                          <div className="flex items-center space-x-4">
                            <span className="flex items-center">
                              <Heart className="w-4 h-4 mr-1" />
                              {project.likes}
                            </span>
                            <span className="flex items-center">
                              <Eye className="w-4 h-4 mr-1" />
                              {project.views}
                            </span>
                            <span className="flex items-center">
                              <Star className="w-4 h-4 mr-1 text-yellow-400" />
                              {project.stars}
                            </span>
                          </div>
                          <span>{project.createdAt}</span>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}

              {activeTab === 'posts' && (
                <div className="space-y-6">
                  {posts.map((post, index) => (
                    <motion.div
                      key={post.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className="bg-dark-800/50 border border-dark-700 rounded-xl p-6"
                    >
                      <p className="text-gray-300 mb-4">{post.content}</p>
                      {post.image && (
                        <img
                          src={post.image}
                          alt="Post"
                          className="w-full h-64 object-cover rounded-lg mb-4"
                        />
                      )}
                      <div className="flex items-center justify-between text-gray-400 text-sm">
                        <div className="flex items-center space-x-4">
                          <span className="flex items-center">
                            <Heart className="w-4 h-4 mr-1" />
                            {post.likes}
                          </span>
                          <span className="flex items-center">
                            <MessageCircle className="w-4 h-4 mr-1" />
                            {post.comments}
                          </span>
                        </div>
                        <span>{post.timeAgo}</span>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}

              {(activeTab === 'liked' || activeTab === 'followers') && (
                <div className="text-center py-12">
                  <div className="text-gray-400 mb-4">Coming soon!</div>
                  <p className="text-gray-500">This section is under development.</p>
                </div>
              )}
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default ProfilePage;
