import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import Sidebar from '../components/Layout/Sidebar';
import TopBar from '../components/Layout/TopBar';
import { Send, Search, Video, Phone, MoreVertical } from 'lucide-react';

const MessagesPage: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [message, setMessage] = useState('');
  const messagesEndRef = useRef<null | HTMLDivElement>(null);

  // Empty contacts and messages - real data will be populated when users start messaging
  const contacts: any[] = [];
  const [activeContact, setActiveContact] = useState(null);
  const messages: any[] = [];

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = () => {
    if (message.trim()) {
      // Logic to send message
      setMessage('');
    }
  };

  return (
    <div className="flex h-screen bg-dark-950">
      <Sidebar />
      <div className="flex-1 flex flex-col ml-64">
        <TopBar onToggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 flex pt-16 overflow-hidden">
          {/* Contacts List */}
          <motion.div
            initial={{ x: -300, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            className="w-1/3 border-r border-dark-700 flex flex-col"
          >
            <div className="p-4 border-b border-dark-700">
              <h2 className="text-xl font-bold text-white mb-4">Messages</h2>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search contacts..."
                  className="w-full pl-10 pr-4 py-2 bg-dark-800 border border-dark-600 rounded-xl focus:outline-none focus:border-neon-blue"
                />
              </div>
            </div>
            <div className="flex-1 overflow-y-auto custom-scrollbar">
              {contacts.map(contact => (
                <div
                  key={contact.id}
                  onClick={() => setActiveContact(contact)}
                  className={`flex items-center p-4 cursor-pointer transition-colors ${
                    activeContact.id === contact.id ? 'bg-neon-blue/10' : 'hover:bg-dark-800'
                  }`}
                >
                  <div className="relative">
                    <img src={contact.avatar} alt={contact.name} className="w-12 h-12 rounded-full" />
                    {contact.isActive && (
                      <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-dark-900 rounded-full" />
                    )}
                  </div>
                  <div className="flex-1 ml-3">
                    <div className="flex justify-between items-center">
                      <h3 className="font-semibold text-white">{contact.name}</h3>
                      <span className="text-xs text-gray-500">{contact.time}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <p className="text-sm text-gray-400 truncate">{contact.lastMessage}</p>
                      {contact.unread > 0 && (
                        <span className="w-5 h-5 bg-neon-pink text-white text-xs rounded-full flex items-center justify-center">
                          {contact.unread}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Chat Window */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex-1 flex flex-col"
          >
            {/* Chat Header */}
            <div className="flex items-center justify-between p-4 border-b border-dark-700">
              <div className="flex items-center">
                <img src={activeContact.avatar} alt={activeContact.name} className="w-10 h-10 rounded-full" />
                <div className="ml-3">
                  <h3 className="font-semibold text-white">{activeContact.name}</h3>
                  <p className="text-sm text-green-400">Online</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <Phone className="w-5 h-5 text-gray-400 hover:text-white cursor-pointer" />
                <Video className="w-5 h-5 text-gray-400 hover:text-white cursor-pointer" />
                <MoreVertical className="w-5 h-5 text-gray-400 hover:text-white cursor-pointer" />
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 p-6 overflow-y-auto custom-scrollbar">
              <div className="space-y-4">
                {messages.map(msg => (
                  <div key={msg.id} className={`flex ${msg.isSender ? 'justify-end' : 'justify-start'}`}>
                    <div
                      className={`max-w-md p-3 rounded-2xl ${
                        msg.isSender
                          ? 'bg-neon-blue text-white rounded-br-none'
                          : 'bg-dark-700 text-gray-300 rounded-bl-none'
                      }`}
                    >
                      <p>{msg.content}</p>
                      <span className="text-xs opacity-70 mt-1 block text-right">{msg.timestamp}</span>
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* Message Input */}
            <div className="p-4 border-t border-dark-700">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Type a message..."
                  value={message}
                  onChange={e => setMessage(e.target.value)}
                  onKeyPress={e => e.key === 'Enter' && handleSendMessage()}
                  className="w-full pr-12 py-3 px-4 bg-dark-800 border border-dark-600 rounded-xl focus:outline-none focus:border-neon-blue"
                />
                <button
                  onClick={handleSendMessage}
                  className="absolute right-3 top-1/2 -translate-y-1/2 p-2 bg-neon-blue rounded-lg text-white disabled:opacity-50"
                  disabled={!message.trim()}
                >
                  <Send className="w-5 h-5" />
                </button>
              </div>
            </div>
          </motion.div>
        </main>
      </div>
    </div>
  );
};

export default MessagesPage;
