import React from 'react';
import { motion } from 'framer-motion';
import { Code, Users } from 'lucide-react';

const AboutSection: React.FC = () => {
  return (
    <section id="about" className="py-20 px-6 scroll-mt-24">
      <div className="max-w-5xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-6xl font-bold mb-4">About DevHub</h2>
          <p className="text-gray-400 max-w-3xl mx-auto">
            DevHub unites developers and designers to collaborate, learn, and showcase their work. Built with modern tools and a focus on community.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          <motion.div initial={{ opacity: 0, y: 20 }} whileInView={{ opacity: 1, y: 0 }} className="p-6 bg-dark-800/50 rounded-2xl border border-dark-700">
            <Code className="w-8 h-8 text-neon-blue mb-4" />
            <h3 className="text-2xl font-semibold mb-2">For Developers</h3>
            <p className="text-gray-400">Share code, get feedback, and discover inspiring projects. Integrations with popular tools and platforms.</p>
          </motion.div>
          <motion.div initial={{ opacity: 0, y: 20 }} whileInView={{ opacity: 1, y: 0 }} className="p-6 bg-dark-800/50 rounded-2xl border border-dark-700">
            <Users className="w-8 h-8 text-neon-purple mb-4" />
            <h3 className="text-2xl font-semibold mb-2">For Designers</h3>
            <p className="text-gray-400">Showcase designs, collaborate on UI/UX, and connect with developers to bring ideas to life.</p>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;

