import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Sidebar from '../components/Layout/Sidebar';
import TopBar from '../components/Layout/TopBar';
import { TrendingUp, Star, Users, Heart, Eye } from 'lucide-react';

const ExplorePage: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Empty arrays - real data will be populated when users create content
  const trendingProjects: any[] = [];
  const featuredCreators: any[] = [];

  const popularTags = ['React', 'UI/UX', 'JavaScript', 'Figma', 'Python', 'Node.js', 'Data Science', 'Animation'];

  return (
    <div className="flex h-screen bg-dark-950">
      <Sidebar />
      <div className="flex-1 flex flex-col ml-64">
        <TopBar onToggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto pt-16">
          <div className="max-w-7xl mx-auto px-6 py-8">
            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
              <h1 className="text-3xl font-bold text-white mb-2">Explore</h1>
              <p className="text-gray-400">Discover what's new and trending in the community.</p>
            </motion.div>

            {/* Trending Projects */}
            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="my-12"
            >
              <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
                <TrendingUp className="w-6 h-6 mr-3 text-neon-blue" />
                Trending Projects
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {trendingProjects.map((project, i) => (
                  <motion.div
                    key={project.id}
                    whileHover={{ y: -5 }}
                    className="group bg-dark-800/50 border border-dark-700 rounded-xl overflow-hidden"
                  >
                    <div className="relative">
                      <img src={project.image} alt={project.title} className="w-full h-40 object-cover" />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
                      <div className="absolute bottom-4 left-4">
                        <h3 className="font-bold text-white group-hover:text-neon-blue transition-colors">{project.title}</h3>
                        <p className="text-sm text-gray-300">by {project.author}</p>
                      </div>
                    </div>
                    <div className="p-4 flex justify-between items-center text-gray-400">
                      <div className="flex items-center space-x-4 text-sm">
                        <span className="flex items-center"><Heart className="w-4 h-4 mr-1 text-neon-pink" /> {project.likes}</span>
                        <span className="flex items-center"><Eye className="w-4 h-4 mr-1" /> {project.views}</span>
                      </div>
                      <motion.button whileHover={{scale: 1.05}} className="px-3 py-1 text-sm bg-dark-700 rounded-lg hover:bg-neon-blue hover:text-white">View</motion.button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.section>

            {/* Featured Creators */}
            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="my-12"
            >
              <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
                <Star className="w-6 h-6 mr-3 text-yellow-400" />
                Featured Creators
              </h2>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-6">
                {featuredCreators.map(creator => (
                  <motion.div
                    key={creator.id}
                    whileHover={{ scale: 1.05 }}
                    className="text-center p-6 bg-dark-800/50 border border-dark-700 rounded-xl"
                  >
                    <img src={creator.avatar} alt={creator.name} className="w-20 h-20 rounded-full mx-auto mb-4" />
                    <h4 className="font-semibold text-white">{creator.name}</h4>
                    <p className="text-sm text-neon-purple mb-2">{creator.role}</p>
                    <p className="text-xs text-gray-400">{creator.followers.toLocaleString()} followers</p>
                    <motion.button whileHover={{scale: 1.05}} className="mt-4 w-full px-3 py-1 text-sm bg-dark-700 rounded-lg hover:bg-neon-purple hover:text-white">Follow</motion.button>
                  </motion.div>
                ))}
              </div>
            </motion.section>

            {/* Popular Tags */}
            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="my-12"
            >
              <h2 className="text-2xl font-bold text-white mb-6">Popular Topics</h2>
              <div className="flex flex-wrap gap-4">
                {popularTags.map(tag => (
                  <motion.button
                    key={tag}
                    whileHover={{ y: -3, scale: 1.05 }}
                    className="px-6 py-3 bg-dark-800/50 border border-dark-700 rounded-xl text-lg font-medium text-gray-300 hover:text-neon-teal hover:border-neon-teal/50"
                  >
                    #{tag}
                  </motion.button>
                ))}
              </div>
            </motion.section>
          </div>
        </main>
      </div>
    </div>
  );
};

export default ExplorePage;
