import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Sidebar from '../components/Layout/Sidebar';
import TopBar from '../components/Layout/TopBar';
import { Users, Search, Plus } from 'lucide-react';

const CommunityPage: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Empty communities - real communities will be created by users
  const communities: any[] = [];

  return (
    <div className="flex h-screen bg-dark-950">
      <Sidebar />
      <div className="flex-1 flex flex-col ml-64">
        <TopBar onToggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto pt-16">
          <div className="max-w-7xl mx-auto px-6 py-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex flex-col md:flex-row md:items-center md:justify-between mb-8"
            >
              <div>
                <h1 className="text-3xl font-bold text-white mb-2">Communities</h1>
                <p className="text-gray-400">Find and join groups of like-minded creators.</p>
              </div>
              <motion.button whileHover={{scale: 1.02}} className="flex items-center space-x-2 mt-4 md:mt-0 px-4 py-2 bg-gradient-to-r from-neon-blue to-neon-purple rounded-lg font-semibold">
                <Plus className="w-4 h-4" />
                <span>Create Community</span>
              </motion.button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="relative mb-8"
            >
              <Search className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search for communities..."
                className="w-full pl-12 pr-4 py-3 bg-dark-800 border border-dark-600 rounded-xl focus:outline-none focus:border-neon-blue"
              />
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {communities.map((community, index) => (
                <motion.div
                  key={community.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 + 0.2 }}
                  whileHover={{ y: -5, boxShadow: '0 10px 20px rgba(0, 0, 0, 0.2)' }}
                  className="bg-dark-800/50 border border-dark-700 rounded-xl overflow-hidden flex flex-col"
                >
                  <img src={community.banner} alt={community.name} className="w-full h-24 object-cover" />
                  <div className="p-6 flex-1 flex flex-col">
                    <h3 className="text-lg font-bold text-white mb-2">{community.name}</h3>
                    <p className="text-gray-400 text-sm mb-4 flex-1">{community.description}</p>
                    <div className="flex items-center space-x-2 text-gray-400 text-sm mb-4">
                      <Users className="w-4 h-4" />
                      <span>{community.members.toLocaleString()} members</span>
                    </div>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {community.tags.map(tag => (
                        <span key={tag} className="px-2 py-1 bg-dark-700 text-neon-blue text-xs rounded-lg">
                          #{tag}
                        </span>
                      ))}
                    </div>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className={`w-full py-2 rounded-lg font-medium transition-colors ${
                        community.isJoined
                          ? 'bg-dark-700 text-white border border-dark-600'
                          : 'bg-neon-blue text-white'
                      }`}
                    >
                      {community.isJoined ? 'Joined' : 'Join'}
                    </motion.button>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default CommunityPage;
