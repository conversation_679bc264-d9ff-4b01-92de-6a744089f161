# PowerShell script to start the Vite development server
$currentDir = $PSScriptRoot
$vitePath = Join-Path -Path $currentDir -ChildPath "node_modules\.bin\vite.ps1"

Write-Host "Starting Vite server from: $vitePath"

# Check if the file exists
if (Test-Path $vitePath) {
    # Execute the Vite PowerShell script directly
    & $vitePath
} else {
    Write-Host "Error: Vite executable not found at $vitePath"
    # Try alternative approach with npx
    Write-Host "Trying with npx..."
    npx --no-install vite
}