import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Sidebar from '../components/Layout/Sidebar';
import TopBar from '../components/Layout/TopBar';
import { UserPlus, Heart, MessageSquare, AtSign, Settings, CheckCheck } from 'lucide-react';

type NotificationType = 'follow' | 'like' | 'comment' | 'mention';

const notificationIcons: Record<NotificationType, React.ElementType> = {
  follow: UserPlus,
  like: Heart,
  comment: MessageSquare,
  mention: AtSign,
};

const notificationColors: Record<NotificationType, string> = {
  follow: 'text-neon-blue',
  like: 'text-neon-pink',
  comment: 'text-neon-teal',
  mention: 'text-neon-purple',
};

const NotificationsPage: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeFilter, setActiveFilter] = useState('all');

  // Empty notifications - real notifications will be generated when users interact
  const mockNotifications: any[] = [];

  const filteredNotifications = mockNotifications.filter(n => {
    if (activeFilter === 'all') return true;
    if (activeFilter === 'unread') return !n.isRead;
    return n.type === activeFilter;
  });

  const filters = [
    { id: 'all', label: 'All' },
    { id: 'unread', label: 'Unread' },
    { id: 'mention', label: 'Mentions' },
  ];

  return (
    <div className="flex h-screen bg-dark-950">
      <Sidebar />
      <div className="flex-1 flex flex-col ml-64">
        <TopBar onToggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto pt-16">
          <div className="max-w-4xl mx-auto px-6 py-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-center justify-between mb-8"
            >
              <h1 className="text-3xl font-bold text-white">Notifications</h1>
              <div className="flex items-center space-x-2">
                <motion.button whileHover={{ scale: 1.05 }} className="px-4 py-2 text-sm bg-dark-800 rounded-lg hover:bg-dark-700">
                  Mark all as read
                </motion.button>
                <motion.button whileHover={{ scale: 1.05 }} className="p-2 bg-dark-800 rounded-lg hover:bg-dark-700">
                  <Settings className="w-4 h-4" />
                </motion.button>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="flex space-x-2 mb-6 p-1 bg-dark-800 rounded-xl"
            >
              {filters.map(filter => (
                <button
                  key={filter.id}
                  onClick={() => setActiveFilter(filter.id)}
                  className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition-colors ${
                    activeFilter === filter.id
                      ? 'bg-neon-blue/20 text-neon-blue'
                      : 'text-gray-400 hover:bg-dark-700'
                  }`}
                >
                  {filter.label}
                </button>
              ))}
            </motion.div>

            <div className="space-y-4">
              {filteredNotifications.map((notification, index) => {
                const Icon = notificationIcons[notification.type];
                const color = notificationColors[notification.type];
                return (
                  <motion.div
                    key={notification.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className={`flex items-start space-x-4 p-4 rounded-xl transition-colors ${
                      notification.isRead
                        ? 'bg-dark-800/50 hover:bg-dark-800'
                        : 'bg-neon-blue/10 border border-neon-blue/20 hover:bg-neon-blue/20'
                    }`}
                  >
                    <div className={`p-2 rounded-full bg-dark-800 ${color}`}>
                      <Icon className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <img
                        src={notification.user.avatar}
                        alt={notification.user.name}
                        className="w-8 h-8 rounded-full inline-block mr-2"
                      />
                      <p className="inline text-gray-300 text-sm">
                        {notification.content}
                      </p>
                      <p className="text-gray-500 text-xs mt-1">{notification.timeAgo}</p>
                    </div>
                    {!notification.isRead && (
                      <div className="w-2.5 h-2.5 bg-neon-blue rounded-full self-center" />
                    )}
                  </motion.div>
                );
              })}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default NotificationsPage;
