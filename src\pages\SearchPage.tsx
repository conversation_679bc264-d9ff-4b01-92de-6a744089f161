import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Sidebar from '../components/Layout/Sidebar';
import TopBar from '../components/Layout/TopBar';
import { 
  Search, 
  Filter, 
  Heart, 
  Star, 
  Eye, 
  MessageCircle,
  Code,
  Palette,
  Users,
  FolderOpen,
  Calendar,
  TrendingUp
} from 'lucide-react';

type SearchFilter = 'all' | 'projects' | 'users' | 'posts';
type SortBy = 'relevance' | 'recent' | 'popular' | 'trending';

const SearchPage: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState<SearchFilter>('all');
  const [sortBy, setSortBy] = useState<SortBy>('relevance');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [searchResults, setSearchResults] = useState<any[]>([]);

  // Initialize with empty results - real data will be populated when users create content
  useEffect(() => {
    setSearchResults([]);
  }, [searchQuery, activeFilter, sortBy, selectedTags]);

  const filters = [
    { id: 'all' as SearchFilter, label: 'All', count: searchResults.length },
    { id: 'projects' as SearchFilter, label: 'Projects', count: searchResults.filter(r => r.type === 'project').length },
    { id: 'users' as SearchFilter, label: 'Users', count: searchResults.filter(r => r.type === 'user').length },
    { id: 'posts' as SearchFilter, label: 'Posts', count: searchResults.filter(r => r.type === 'post').length },
  ];

  const popularTags = [
    'React', 'TypeScript', 'JavaScript', 'Python', 'UI/UX', 'Design',
    'CSS', 'HTML', 'Node.js', 'Vue.js', 'Angular', 'Figma'
  ];

  const filteredResults = searchResults.filter(result => {
    if (activeFilter === 'all') return true;
    return result.type === activeFilter;
  });

  const toggleTag = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  return (
    <div className="flex h-screen bg-dark-950">
      <Sidebar />
      
      <div className="flex-1 flex flex-col ml-64">
        <TopBar onToggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        
        <main className="flex-1 overflow-y-auto pt-16">
          <div className="max-w-7xl mx-auto px-6 py-8">
            {/* Search Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-8"
            >
              <h1 className="text-3xl font-bold text-white mb-2">Search & Discover</h1>
              <p className="text-gray-400">Find amazing projects, talented creators, and inspiring content</p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              {/* Filters Sidebar */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
                className="space-y-6"
              >
                {/* Search Input */}
                <div className="bg-dark-800/50 border border-dark-700 rounded-xl p-6">
                  <div className="relative mb-4">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search anything..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-neon-blue transition-colors"
                    />
                  </div>
                </div>

                {/* Content Type Filters */}
                <div className="bg-dark-800/50 border border-dark-700 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                    <Filter className="w-5 h-5 mr-2" />
                    Filter by Type
                  </h3>
                  <div className="space-y-2">
                    {filters.map(filter => (
                      <motion.button
                        key={filter.id}
                        whileHover={{ x: 5 }}
                        onClick={() => setActiveFilter(filter.id)}
                        className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors ${
                          activeFilter === filter.id
                            ? 'bg-neon-blue/20 text-neon-blue border border-neon-blue/30'
                            : 'text-gray-400 hover:text-white hover:bg-dark-700'
                        }`}
                      >
                        <span>{filter.label}</span>
                        <span className="text-sm">{filter.count}</span>
                      </motion.button>
                    ))}
                  </div>
                </div>

                {/* Sort Options */}
                <div className="bg-dark-800/50 border border-dark-700 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-white mb-4">Sort By</h3>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as SortBy)}
                    className="w-full px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white focus:outline-none focus:border-neon-blue transition-colors"
                  >
                    <option value="relevance">Most Relevant</option>
                    <option value="recent">Most Recent</option>
                    <option value="popular">Most Popular</option>
                    <option value="trending">Trending</option>
                  </select>
                </div>

                {/* Popular Tags */}
                <div className="bg-dark-800/50 border border-dark-700 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-white mb-4">Popular Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {popularTags.map(tag => (
                      <motion.button
                        key={tag}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => toggleTag(tag)}
                        className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                          selectedTags.includes(tag)
                            ? 'bg-neon-blue/20 text-neon-blue border border-neon-blue/30'
                            : 'bg-dark-700 text-gray-300 hover:text-neon-blue hover:bg-dark-600'
                        }`}
                      >
                        #{tag}
                      </motion.button>
                    ))}
                  </div>
                </div>
              </motion.div>

              {/* Search Results */}
              <div className="lg:col-span-3">
                {/* Results Header */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="flex items-center justify-between mb-6"
                >
                  <div>
                    <h2 className="text-xl font-semibold text-white">
                      {filteredResults.length} results found
                    </h2>
                    {searchQuery && (
                      <p className="text-gray-400">for "{searchQuery}"</p>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-4 h-4 text-neon-blue" />
                    <span className="text-sm text-gray-400">Sorted by {sortBy}</span>
                  </div>
                </motion.div>

                {/* Results Grid */}
                <div className="space-y-6">
                  {filteredResults.map((result, index) => (
                    <motion.div
                      key={`${result.type}-${result.id}`}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className="bg-dark-800/50 border border-dark-700 rounded-xl p-6 hover:border-dark-600 transition-colors"
                    >
                      {/* Project Result */}
                      {result.type === 'project' && (
                        <div className="flex space-x-4">
                          <motion.img
                            whileHover={{ scale: 1.05 }}
                            src={result.image}
                            alt={result.title}
                            className="w-24 h-20 object-cover rounded-lg cursor-pointer"
                          />
                          <div className="flex-1">
                            <div className="flex items-start justify-between">
                              <div>
                                <h3 className="text-lg font-semibold text-white mb-2 hover:text-neon-blue cursor-pointer">
                                  {result.title}
                                </h3>
                                <p className="text-gray-400 text-sm mb-3 line-clamp-2">
                                  {result.description}
                                </p>
                                <div className="flex items-center space-x-4 text-sm text-gray-400 mb-3">
                                  <span className="flex items-center">
                                    <Heart className="w-4 h-4 mr-1" />
                                    {result.likes}
                                  </span>
                                  <span className="flex items-center">
                                    <Eye className="w-4 h-4 mr-1" />
                                    {result.views}
                                  </span>
                                  <span className="flex items-center">
                                    <Star className="w-4 h-4 mr-1 text-yellow-400" />
                                    {result.stars}
                                  </span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <img
                                    src={result.author.avatar}
                                    alt={result.author.name}
                                    className="w-6 h-6 rounded-full"
                                  />
                                  <span className="text-gray-400 text-sm">
                                    by {result.author.name}
                                  </span>
                                </div>
                              </div>
                              <div className="flex items-center space-x-2">
                                <FolderOpen className="w-4 h-4 text-neon-blue" />
                                <span className="text-neon-blue text-sm">Project</span>
                              </div>
                            </div>
                            <div className="flex flex-wrap gap-2 mt-3">
                              {result.tags.map((tag: string) => (
                                <span
                                  key={tag}
                                  className="px-2 py-1 bg-dark-700 text-neon-blue text-xs rounded-lg"
                                >
                                  #{tag}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* User Result */}
                      {result.type === 'user' && (
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <motion.img
                              whileHover={{ scale: 1.05 }}
                              src={result.avatar}
                              alt={result.name}
                              className="w-16 h-16 rounded-full cursor-pointer"
                            />
                            <div>
                              <h3 className="text-lg font-semibold text-white hover:text-neon-blue cursor-pointer">
                                {result.name}
                              </h3>
                              <div className="flex items-center space-x-2 mb-2">
                                <span className="text-gray-400 text-sm">@{result.username}</span>
                                <span className={`px-2 py-1 text-xs rounded-full ${
                                  result.role === 'Developer' 
                                    ? 'bg-neon-blue/20 text-neon-blue' 
                                    : 'bg-neon-purple/20 text-neon-purple'
                                }`}>
                                  {result.role === 'Developer' ? <Code className="w-3 h-3 inline mr-1" /> : <Palette className="w-3 h-3 inline mr-1" />}
                                  {result.role}
                                </span>
                              </div>
                              <p className="text-gray-400 text-sm mb-2">{result.bio}</p>
                              <div className="flex items-center space-x-4 text-sm text-gray-400">
                                <span>{result.followers} followers</span>
                                <span>{result.projects} projects</span>
                              </div>
                              <div className="flex flex-wrap gap-1 mt-2">
                                {result.skills.slice(0, 3).map((skill: string) => (
                                  <span
                                    key={skill}
                                    className="px-2 py-1 bg-dark-700 text-gray-300 text-xs rounded"
                                  >
                                    {skill}
                                  </span>
                                ))}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            <div className="flex items-center space-x-2">
                              <Users className="w-4 h-4 text-neon-purple" />
                              <span className="text-neon-purple text-sm">User</span>
                            </div>
                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                                result.isFollowing
                                  ? 'bg-dark-700 text-white border border-dark-600'
                                  : 'bg-neon-blue text-white'
                              }`}
                            >
                              {result.isFollowing ? 'Following' : 'Follow'}
                            </motion.button>
                          </div>
                        </div>
                      )}

                      {/* Post Result */}
                      {result.type === 'post' && (
                        <div>
                          <div className="flex items-start space-x-3 mb-4">
                            <img
                              src={result.author.avatar}
                              alt={result.author.name}
                              className="w-10 h-10 rounded-full"
                            />
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                <span className="font-semibold text-white">{result.author.name}</span>
                                <span className={`px-2 py-1 text-xs rounded-full ${
                                  result.author.role === 'Developer' 
                                    ? 'bg-neon-blue/20 text-neon-blue' 
                                    : 'bg-neon-purple/20 text-neon-purple'
                                }`}>
                                  {result.author.role === 'Developer' ? <Code className="w-3 h-3 inline mr-1" /> : <Palette className="w-3 h-3 inline mr-1" />}
                                  {result.author.role}
                                </span>
                                <span className="text-gray-500 text-sm">{result.timeAgo}</span>
                              </div>
                              <p className="text-gray-300 mb-3">{result.content}</p>
                              {result.image && (
                                <motion.img
                                  whileHover={{ scale: 1.02 }}
                                  src={result.image}
                                  alt="Post content"
                                  className="w-full h-48 object-cover rounded-lg mb-3 cursor-pointer"
                                />
                              )}
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4 text-sm text-gray-400">
                                  <span className="flex items-center">
                                    <Heart className="w-4 h-4 mr-1" />
                                    {result.likes}
                                  </span>
                                  <span className="flex items-center">
                                    <MessageCircle className="w-4 h-4 mr-1" />
                                    {result.comments}
                                  </span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <MessageCircle className="w-4 h-4 text-neon-teal" />
                                  <span className="text-neon-teal text-sm">Post</span>
                                </div>
                              </div>
                              {result.tags && result.tags.length > 0 && (
                                <div className="flex flex-wrap gap-2 mt-3">
                                  {result.tags.map((tag: string) => (
                                    <span
                                      key={tag}
                                      className="px-2 py-1 bg-dark-700 text-neon-blue text-xs rounded-lg"
                                    >
                                      #{tag}
                                    </span>
                                  ))}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>

                {/* Load More */}
                {filteredResults.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5 }}
                    className="text-center py-8"
                  >
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="px-8 py-3 bg-dark-800 border border-dark-600 rounded-xl text-white hover:border-neon-blue transition-colors"
                    >
                      Load More Results
                    </motion.button>
                  </motion.div>
                )}

                {/* Empty State */}
                {filteredResults.length === 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center py-12"
                  >
                    <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-white mb-2">No results found</h3>
                    <p className="text-gray-400">
                      Try adjusting your search terms or filters to find what you're looking for.
                    </p>
                  </motion.div>
                )}
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default SearchPage;
