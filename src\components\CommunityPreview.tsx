import React, { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { Heart, MessageCircle, Share, Star, Code, Palette, Users } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const CommunityPreview: React.FC = () => {
  const ref = useRef(null);
  const navigate = useNavigate();
  const isInView = useInView(ref, { once: true, margin: '-50px' });

  // Empty arrays - real data will be populated when users create content
  const mockPosts: any[] = [];
  const mockProjects: any[] = [];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  return (
    <section id="community" ref={ref} className="py-20 px-6 scroll-mt-24">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="max-w-7xl mx-auto"
      >
        {/* Section Header */}
        <motion.div variants={itemVariants} className="text-center mb-16">
          <h2 className="text-4xl md:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              Join Our Thriving
            </span>
            <br />
            <span className="bg-gradient-to-r from-neon-teal via-neon-blue to-neon-purple bg-clip-text text-transparent">
              Creative Community
            </span>
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            See what amazing projects and collaborations are happening right now in our community.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {/* Community Feed */}
          <motion.div variants={itemVariants} className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
              <MessageCircle className="w-6 h-6 text-neon-blue mr-2" />
              Latest Community Posts
            </h3>
            
            <div className="space-y-4 max-h-96 overflow-y-auto custom-scrollbar">
              {mockPosts.map((post, index) => (
                <motion.div
                  key={post.id}
                  variants={itemVariants}
                  whileHover={{ scale: 1.02 }}
                  className="p-4 bg-dark-800/50 rounded-xl border border-dark-700 hover:border-neon-blue/30 transition-all duration-300"
                >
                  <div className="flex items-start space-x-3">
                    <img
                      src={post.author.avatar}
                      alt={post.author.name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="font-semibold text-white text-sm">{post.author.name}</span>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          post.author.role === 'Developer' 
                            ? 'bg-neon-blue/20 text-neon-blue' 
                            : 'bg-neon-purple/20 text-neon-purple'
                        }`}>
                          {post.author.role === 'Developer' ? <Code className="w-3 h-3 inline mr-1" /> : <Palette className="w-3 h-3 inline mr-1" />}
                          {post.author.role}
                        </span>
                        <span className="text-gray-500 text-xs">{post.timeAgo}</span>
                      </div>
                      <p className="text-gray-300 text-sm mb-3">{post.content}</p>
                      <div className="flex items-center space-x-4 text-gray-400 text-xs">
                        <span className="flex items-center space-x-1 hover:text-neon-pink cursor-pointer">
                          <Heart className="w-4 h-4" />
                          <span>{post.likes}</span>
                        </span>
                        <span className="flex items-center space-x-1 hover:text-neon-blue cursor-pointer">
                          <MessageCircle className="w-4 h-4" />
                          <span>{post.comments}</span>
                        </span>
                        <span className="flex items-center space-x-1 hover:text-neon-teal cursor-pointer">
                          <Share className="w-4 h-4" />
                          <span>{post.shares}</span>
                        </span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Featured Projects */}
          <motion.div variants={itemVariants} className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
              <Star className="w-6 h-6 text-neon-purple mr-2" />
              Featured Projects
            </h3>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {mockProjects.map((project, index) => (
                <motion.div
                  key={project.id}
                  variants={itemVariants}
                  whileHover={{ y: -5, scale: 1.02 }}
                  className="group bg-dark-800/50 rounded-xl border border-dark-700 hover:border-neon-purple/30 transition-all duration-300 overflow-hidden"
                >
                  <div className="aspect-video bg-gradient-to-br from-neon-blue/10 to-neon-purple/10 relative overflow-hidden">
                    <img
                      src={project.image}
                      alt={project.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute top-2 left-2">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        project.category === 'Development' 
                          ? 'bg-neon-blue/20 text-neon-blue backdrop-blur-sm' 
                          : 'bg-neon-purple/20 text-neon-purple backdrop-blur-sm'
                      }`}>
                        {project.technology}
                      </span>
                    </div>
                  </div>
                  <div className="p-4">
                    <h4 className="font-semibold text-white mb-2 truncate">{project.title}</h4>
                    <p className="text-gray-400 text-sm mb-3 line-clamp-2">{project.description}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-500 text-xs">by {project.author}</span>
                      <span className="flex items-center space-x-1 text-yellow-400 text-xs">
                        <Star className="w-3 h-3 fill-current" />
                        <span>{project.stars}</span>
                      </span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Community Stats */}
        <motion.div
          variants={itemVariants}
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12"
        >
          {[
            { label: 'Active Members', value: '15.2K', icon: Users },
            { label: 'Projects Shared', value: '8.5K', icon: Code },
            { label: 'Collaborations', value: '3.2K', icon: Share },
            { label: 'Success Stories', value: '1.8K', icon: Star },
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              whileHover={{ y: -5 }}
              className="text-center p-6 bg-dark-800/30 rounded-xl border border-dark-700"
            >
              <stat.icon className="w-8 h-8 text-neon-blue mx-auto mb-2" />
              <div className="text-3xl font-bold text-white mb-1">{stat.value}</div>
              <div className="text-gray-400 text-sm">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA */}
        <motion.div variants={itemVariants} className="text-center">
          <motion.button
            whileHover={{
              scale: 1.05,
              boxShadow: '0 0 30px rgba(20, 184, 166, 0.4)'
            }}
            whileTap={{ scale: 0.95 }}
            onClick={() => navigate('/register')}
            className="px-8 py-4 bg-gradient-to-r from-neon-teal to-neon-blue rounded-full font-semibold text-lg shadow-xl"
          >
            Join the Community
          </motion.button>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default CommunityPreview;
